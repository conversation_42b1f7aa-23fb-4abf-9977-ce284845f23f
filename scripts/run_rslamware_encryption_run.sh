#!/bin/bash

# RSlamware Encrypted Execution Script
# This script runs the encrypted rslamware system in mapping or localization mode

SELF="$(which $0)"
SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"
RSLAMWARE_ROOT="$SCRIPTS_ROOT/.."

mapping=False
use_als=False
use_cartographer_localization=True
explore=False
scan_topic="fusion_scan"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m[INFO]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $1"
}

# Parse command line arguments
for arg in "$@"
do
    if [ "$arg" == "mapping" ]; then
        mapping=True
    fi
    if [ "$arg" == "use_als" ]; then
        use_als=True
        use_cartographer_localization=False
    fi
    if [ "$arg" == "use_cartographer_localization" ]; then
        use_cartographer_localization=True
        use_als=False
    fi
    if [ "$arg" == "explore" ]; then
        explore=True
    fi
done

print_status "=========================================="
print_status "RSlamware Encrypted Execution"
print_status "=========================================="
print_status "Workspace: $RSLAMWARE_ROOT"
print_status "Mode: $([ "$mapping" == "True" ] && echo "MAPPING" || echo "LOCALIZATION")"
print_status "Scan topic: $scan_topic"
print_status "Use ALS: $use_als"
print_status "Use Cartographer Localization: $use_cartographer_localization"
print_status "Explore: $explore"
print_status "=========================================="
echo ""

# Check if encrypted file exists
if [ ! -f "$RSLAMWARE_ROOT/rslamware.enc" ]; then
    print_error "Encrypted file not found: $RSLAMWARE_ROOT/rslamware.enc"
    print_error "Please run build_rslamware_encryption_run.sh first."
    exit 1
fi

# Check if encryption tool exists
ENCRYPTION_TOOL="$RSLAMWARE_ROOT/install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware_encryption_run"
if [ ! -f "$ENCRYPTION_TOOL" ]; then
    print_error "Encryption tool not found: $ENCRYPTION_TOOL"
    print_error "Please run build_rslamware_encryption_run.sh first."
    exit 1
fi

# Source ROS2 environment
print_status "Sourcing ROS2 environment..."
if [ -f "/opt/ros/humble/setup.bash" ]; then
    source /opt/ros/humble/setup.bash
else
    print_error "ROS2 Humble not found! Please install ROS2 Humble."
    exit 1
fi

# Source workspace
if [ -f "$RSLAMWARE_ROOT/install/setup.bash" ]; then
    source "$RSLAMWARE_ROOT/install/setup.bash"
else
    print_error "Workspace not built! Please run build_rslamware_encryption_run.sh first."
    exit 1
fi

print_status "Environment sourced successfully."
echo ""

# Change to workspace directory
cd "$RSLAMWARE_ROOT"

# Run the encrypted system
print_status "Starting encrypted rslamware system..."

if [ "$mapping" == "True" ]; then
    print_status "Running in MAPPING mode..."
    "$ENCRYPTION_TOOL" --run --mapping
else
    print_status "Running in LOCALIZATION mode..."
    "$ENCRYPTION_TOOL" --run --localization
fi

if [ $? -eq 0 ]; then
    print_status "Encrypted rslamware execution completed successfully!"
else
    print_error "Encrypted rslamware execution failed!"
    exit 1
fi
