from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node

def generate_launch_description():
    return LaunchDescription([
        # 声明所有可配置参数
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation time if true'),
        DeclareLaunchArgument(
            'odom_frame',
            default_value='odom',
            description='Odometry frame id'),
        DeclareLaunchArgument(
            'lidar_count',
            default_value='2',
            description='Number of lidars'),
            
        DeclareLaunchArgument(
            'lidar0_scan_sub_topic',
            default_value='/lidar_front/scan',
            description='Topic to subscribe for lidar0 scan'),
        DeclareLaunchArgument(
            'lidar0_scan_pub_topic',
            default_value='/lidar_front/scan_undistortion',
            description='Topic to publish undistorted lidar0 scan'),
        DeclareLaunchArgument(
            'lidar0_lidar_frame',
            default_value='rplidar_front',
            description='Frame id for lidar0'),
        DeclareLaunchArgument(
            'lidar0_lidar_scan_time_gain',
            default_value='1.0',
            description='Scan time gain for lidar0'),
            
        DeclareLaunchArgument(
            'lidar1_scan_sub_topic',
            default_value='/lidar_back/scan',
            description='Topic to subscribe for lidar1 scan'),
        DeclareLaunchArgument(
            'lidar1_scan_pub_topic',
            default_value='/lidar_back/scan_undistortion',
            description='Topic to publish undistorted lidar1 scan'),
        DeclareLaunchArgument(
            'lidar1_lidar_frame',
            default_value='rplidar_back',
            description='Frame id for lidar1'),
        DeclareLaunchArgument(
            'lidar1_lidar_scan_time_gain',
            default_value='1.0',
            description='Scan time gain for lidar1'),

        Node(
            package='lidar_undistortion_2d',
            executable='lidar_undistortion_2d_node',
            name='lidar_undistortion_2d_node',
            parameters=[{
                'odom_frame': LaunchConfiguration('odom_frame'),
                'lidar_count': LaunchConfiguration('lidar_count'),
                
                'lidar0.scan_sub_topic': LaunchConfiguration('lidar0_scan_sub_topic'),
                'lidar0.scan_pub_topic': LaunchConfiguration('lidar0_scan_pub_topic'),
                'lidar0.lidar_frame': LaunchConfiguration('lidar0_lidar_frame'),
                'lidar0.lidar_scan_time_gain': LaunchConfiguration('lidar0_lidar_scan_time_gain'),
                
                'lidar1.scan_sub_topic': LaunchConfiguration('lidar1_scan_sub_topic'),
                'lidar1.scan_pub_topic': LaunchConfiguration('lidar1_scan_pub_topic'),
                'lidar1.lidar_frame': LaunchConfiguration('lidar1_lidar_frame'),
                'lidar1.lidar_scan_time_gain': LaunchConfiguration('lidar1_lidar_scan_time_gain'),
                
                'use_sim_time': LaunchConfiguration('use_sim_time')
            }],
            output='screen'
        )
    ])