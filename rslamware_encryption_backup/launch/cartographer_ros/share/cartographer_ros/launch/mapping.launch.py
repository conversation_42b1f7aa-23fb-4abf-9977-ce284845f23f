import os

from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, ExecuteProcess
from launch.actions import IncludeLaunchDescription
from launch.actions import Register<PERSON>vent<PERSON>and<PERSON>, Shutdown, EmitEvent
from launch.actions import SetEnvironmentVariable
from launch.events import Shutdown as ShutdownEvent
from launch.event_handlers import OnProcessExit, OnShutdown
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PythonExpression, PathJoinSubstitution, TextSubstitution
from launch.substitutions import ThisLaunchFileDir
from launch_ros.actions import Node
from launch.actions import TimerAction


def generate_launch_description():
    use_sim_time = LaunchConfiguration('use_sim_time')
    cartographer_ros_prefix = get_package_share_directory('cartographer_ros')
    bringup_dir = get_package_share_directory('rslamware_bringup')
    simulator_dir = get_package_share_directory('simulator')
    cartographer_config_dir = LaunchConfiguration('cartographer_config_dir', default=os.path.join(
                                                  cartographer_ros_prefix, 'configuration_files'))
    configuration_basename = LaunchConfiguration('configuration_basename',
                                                 default='mapping_2d.lua')

    resolution = LaunchConfiguration('resolution', default='0.05')
    publish_period_sec = LaunchConfiguration('publish_period_sec', default='1.0')
    explore = LaunchConfiguration('explore', default='False')

    nav2_params = PythonExpression([
        "f'",
        TextSubstitution(text=str(simulator_dir)), "/params/turtlebot3.yaml'",
        " if '", LaunchConfiguration('mode'), "' == 'simulation' else '",
        TextSubstitution(text=str(bringup_dir)), "/'combined_config.yaml''"
    ])

    # 包含occupancy_grid.launch.py
    occupancy_grid_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([ThisLaunchFileDir(), '/occupancy_grid.launch.py']),
        launch_arguments={'use_sim_time': use_sim_time, 'resolution': resolution,
                          'publish_period_sec': publish_period_sec}.items(),
    )

    navigation_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            PathJoinSubstitution([
                get_package_share_directory('nav2_bringup'),
                'launch/navigation_launch.py'
            ])
        ),
        launch_arguments={'params_file': nav2_params,
            'use_sim_time': LaunchConfiguration('use_sim_time')}.items()
    )
    
    # 创建关闭监听节点
    shutdown_node = Node(
        package='shutdown_listener',
        executable='shutdown_listener',
        name='shutdown_listener',
        output='screen'
    )

    # 添加关闭处理器，当shutdown_node退出时，发送全局关闭事件
    shutdown_handler = RegisterEventHandler(
        OnProcessExit(
            target_action=shutdown_node,
            on_exit=[
                # 发送全局关闭事件，确保所有子launch文件中的进程也能被终止
                EmitEvent(event=ShutdownEvent(reason='Shutdown signal received'))
            ]
        )
    )

    # 添加关闭事件处理器，确保所有进程在关闭时都能被正确终止
    global_shutdown_handler = RegisterEventHandler(
        OnShutdown(
            on_shutdown=[
                # 记录关闭信息
                ExecuteProcess(
                    cmd=['echo', 'Shutting down all processes...'],
                    output='screen'
                )
            ]
        )
    )

    return LaunchDescription([
        DeclareLaunchArgument(
            name='mode',
            default_value='real',
            description='Operation mode: real | simulation | replay',
            choices=['real', 'simulation', 'replay']),
        DeclareLaunchArgument(
            'cartographer_config_dir',
            default_value=cartographer_config_dir,
            description='Full path to config file to load'),
        DeclareLaunchArgument(
            'configuration_basename',
            default_value=configuration_basename,
            description='Name of lua file for cartographer'),
        DeclareLaunchArgument(
            'use_sim_time',
            default_value=PythonExpression(['"true" if "', LaunchConfiguration('mode'), '" == "simulation" else "false"']),
            description='Use simulation time'
        ),
        DeclareLaunchArgument(
            'scan_topic',
            default_value='fusion_scan',
            description='Topic name for laser scan data'),

        Node(
            package='cartographer_ros',
            executable='cartographer_node',
            name='cartographer_node',
            remappings = [
            # ('scan_1', 'lidar_front/scan_filtered'),
            # ('scan_2', 'lidar_back/scan_filtered')
              ('scan', LaunchConfiguration('scan_topic'))],
            output='screen',
            parameters=[{'use_sim_time': use_sim_time}],
            arguments=['-configuration_directory', cartographer_config_dir,
                       '-configuration_basename', configuration_basename]),

        DeclareLaunchArgument(
            'resolution',
            default_value=resolution,
            description='Resolution of a grid cell in the published occupancy grid'),

        DeclareLaunchArgument(
            'publish_period_sec',
            default_value=publish_period_sec,
            description='OccupancyGrid publishing period'),

        occupancy_grid_launch,
        navigation_launch,

        #launch explore.launch.py
        TimerAction(
            period=5.0,
            actions=[
                IncludeLaunchDescription(
                    PythonLaunchDescriptionSource(
                PathJoinSubstitution([
                    get_package_share_directory('explore_lite'),
                    'launch/explore.launch.py'
                ])
            ),
            condition=IfCondition(explore),
            launch_arguments={'use_sim_time': LaunchConfiguration('use_sim_time'),
                        'namespace': LaunchConfiguration('namespace')}.items()
                )
            ]
        ),

        # 添加关闭监听节点和处理器
        shutdown_node,
        shutdown_handler,
        global_shutdown_handler
    ])
