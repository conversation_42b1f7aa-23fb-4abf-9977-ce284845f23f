import launch
from launch import LaunchDescription
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory
import os
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import ThisLaunchFileDir, PathJoinSubstitution
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    pkg_dir = get_package_share_directory('sl_chassis_ros')

    robot_localization_launch_path = PathJoinSubstitution([
        ThisLaunchFileDir(),  # 当前launch文件所在目录
        'robot_localization_ekf.launch.py'     # 子launch文件名
    ])


    return LaunchDescription([
        DeclareLaunchArgument(
            'odom_topic',
            default_value='/odom',
            description='odometry frame published by the robot'
        ),

        DeclareLaunchArgument(
            'cmd_vel_topic',
            default_value='/cmd_vel',
            description='cmd_vel frame received by the robot'
        ),



        Node(
            package='sl_chassis_ros',
            executable='sl_chassis_base_node',
            name='sl_chassis_base_node',
            parameters=[{'frequency': 50.0}, {'pub_tf': False}],  # Set the frequency to  Hz
            remappings=[
                ('/sl_chassis/cmd_vel', LaunchConfiguration('cmd_vel_topic')), 
                ('/odometry/filtered', LaunchConfiguration('odom_topic')),
            ]
        ),

        
        Node(
            package='sl_chassis_ros',
            executable='sl_chassis_imu_node',
            name='sl_chassis_imu_node',
            parameters=[{'pub_tf': True}],  # 
        ),


        IncludeLaunchDescription(
            PythonLaunchDescriptionSource([robot_localization_launch_path]),
            launch_arguments={
                'odom_topic': LaunchConfiguration('odom_topic'),
            }.items()
        )
        # Node(
        #     package='robot_localization',
        #     executable='ekf_node',
        #     name='ekf_node',
        #     # output='screen',
        #     parameters=[os.path.join(pkg_dir, 'config', 'ekf_config.yaml'), {'use_sim_time': False}]
        # )

    ])

if __name__ == '__main__':
    generate_launch_description()
