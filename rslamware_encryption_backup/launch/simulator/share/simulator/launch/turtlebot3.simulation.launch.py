import os
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from ament_index_python.packages import get_package_share_directory
from launch.substitutions import LaunchConfiguration
from launch.actions import DeclareLaunchArgument, LogInfo
from launch_ros.actions import Node

def generate_launch_description():    
    packge_dir = get_package_share_directory('simulator')
    turtlebot3_launch = os.path.join(packge_dir, 'launch', 'turtlebot3_world.launch.py') 
    
    nav2_launch = os.path.join(get_package_share_directory('nav2_bringup'), 'launch', 'bringup_launch.py')  
    
    rviz_config_dir = os.path.join(get_package_share_directory('nav2_bringup'), 'rviz', 'nav2_default_view.rviz')

    return LaunchDescription([ 
        DeclareLaunchArgument(
            'map',
            default_value=os.path.join( packge_dir, 'map', 'map.yaml'),
            description='Full path to map file to load'),

        DeclareLaunchArgument(
            'params_file',
            default_value=os.path.join( packge_dir, 'params', 'turtlebot3.yaml'),
            description='Full path to param file to load'),

        DeclareLaunchArgument(
            'use_sim_time',
            default_value='true',
            description='Use simulation (Gazebo) clock if true'),

        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(turtlebot3_launch)
        ),
 
        LogInfo(msg=['map: ', LaunchConfiguration('map')]),
        LogInfo(msg=['params_file: ', LaunchConfiguration('params_file')]), 

        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(nav2_launch),
            launch_arguments={
                'map': LaunchConfiguration('map'),
                'use_sim_time': LaunchConfiguration('use_sim_time'),
                'params_file': LaunchConfiguration('params_file')}.items(), 
        ),

        Node(
            package='rviz2',
            executable='rviz2',
            name='rviz2',
            arguments=['-d', rviz_config_dir],
            parameters=[{'use_sim_time': LaunchConfiguration('use_sim_time')}],
            output='screen'),
    ])
