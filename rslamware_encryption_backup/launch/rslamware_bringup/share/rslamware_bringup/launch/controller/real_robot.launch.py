import os
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch_ros.actions import Node
from launch.launch_description_sources import PythonLaunchDescriptionSource
from ament_index_python.packages import get_package_share_directory
import yaml

def generate_launch_description():
    config_dir = os.path.join(get_package_share_directory('rslamware_bringup'), 'config') 
    urdf_file = os.path.join(config_dir, 'robot.urdf')
    common_topics_path = os.path.join(config_dir, 'topics.yaml')

    with open(common_topics_path, 'r') as f:
        topic_config = yaml.safe_load(f)['topics']['ros__parameters']
 
    front_laser_topic = topic_config['front_laser_scan']
    back_laser_topic = topic_config['back_laser_scan']
    filtered_front_laser_topic = topic_config['filtered_front_laser_scan']
    filtered_back_laser_topic = topic_config['filtered_back_laser_scan']
    fusion_laser_topic = topic_config['fusion_scan']
    undistortion_front_laser_scan = topic_config['undistortion_front_laser_scan']
    undistortion_back_laser_scan = topic_config['undistortion_back_laser_scan']
    #add other launch
    return LaunchDescription([
        Node(
            package='robot_state_publisher',
            executable='robot_state_publisher',
            name='robot_state_publisher',
            output='screen',
            parameters=[{'robot_description': open(urdf_file).read()}]
        ),
        
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(get_package_share_directory('sl_vcu_all'), 'launch/sl_vcu_all.launch.py')
            )
        ),

        Node(
            package='rplidar_ros',
            executable='rplidar_node',
            name='rplidar_node_front',
            parameters=[ 
                common_topics_path,
                os.path.join(config_dir, 'rplidar.yaml'),
                {'topic_name': front_laser_topic},
            ], 
            respawn=True,
            respawn_delay=5.0,
            output='screen'
        ), 

        Node(
            package='rplidar_ros',
            executable='rplidar_node',
            name='rplidar_node_back',
            parameters=[ 
                common_topics_path,
                os.path.join(config_dir, 'rplidar.yaml'),
                {'topic_name': back_laser_topic},
            ], 
            respawn=True,
            respawn_delay=5.0,
            output='screen'
        ),

        Node(
            package="laser_filters",
            executable="scan_to_scan_filter_chain",
            name='laser_filter_front',
            parameters=[os.path.join(config_dir, 'laser_filter.yaml')], 
            remappings=[ ('scan', front_laser_topic),
                ('scan_filtered', filtered_front_laser_topic),
            ],
            prefix="taskset -c 4-7",
            output='screen'
        ),

        Node(
            package="laser_filters",
            executable="scan_to_scan_filter_chain",
            name='laser_filter_back',
            parameters=[os.path.join(config_dir, 'laser_filter.yaml')], 
            remappings=[ ('scan', back_laser_topic),
                ('scan_filtered', filtered_back_laser_topic),
            ],
            prefix="taskset -c 4-7",
            output='screen'
        ),
        
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(get_package_share_directory('ascamera'), 'launch', 'multi_nuwa.launch.py')
           ),
        ),
        
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(get_package_share_directory('depth_process'), 'launch', 'depth_filter.launch.py')
           ),
        ),
        
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(get_package_share_directory('multi_lidar_data_sync'), 'launch', 'multi_lidar_data_sync.launch.py')
            ),
            launch_arguments={
                'main_scan_topic': undistortion_front_laser_scan,
                'sub_scan_topic': undistortion_back_laser_scan,
                'scan_pub_topic': fusion_laser_topic,
            }.items(),
        ),

        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
               os.path.join(get_package_share_directory('lidar_undistortion_2d'), 'launch', 'lidar_undistortion_2d.launch.py')
            ),
            launch_arguments={
                'lidar0_scan_sub_topic': filtered_front_laser_topic,
                'lidar0_scan_pub_topic': undistortion_front_laser_scan,
                'lidar1_scan_sub_topic': filtered_back_laser_topic,
                'lidar1_scan_pub_topic': undistortion_back_laser_scan,
            }.items(),
        ),
    ])
