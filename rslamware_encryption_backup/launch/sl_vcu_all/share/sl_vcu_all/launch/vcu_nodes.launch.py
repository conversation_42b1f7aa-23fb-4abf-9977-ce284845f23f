from launch import LaunchDescription
from launch.substitutions import PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare

def generate_launch_description():
    return LaunchDescription([
        Node(
            package='sl_vcu_all',
            executable='can_frame_dispatcher_node',
            name='can_frame_dispatcher',
            output='screen'
        ),
        Node(
            package='sl_vcu_all',
            executable='zl_motor_controller_node',
            name='zl_motor_controller',
            output='screen'
        ),
        Node(
            package='sl_vcu_all',
            executable='bumper_sensor_node',
            name='bumper_sensor',
            output='screen'
        ),
        Node(
            package='sl_vcu_all',
            executable='imu_sensor_node',
            name='imu_sensor',
            parameters=[PathJoinSubstitution([
                FindPackageShare('sl_vcu_all'),
                'config',
                'imu_sensor.yaml'
            ])],
            output='screen'
        )
    ])

if __name__ == '__main__':
    generate_launch_description()

