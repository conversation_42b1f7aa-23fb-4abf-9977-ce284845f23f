#!/usr/bin/env python3

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    # Declare launch arguments
    config_arg = DeclareLaunchArgument(
        'config_file',
        default_value=PathJoinSubstitution([
            FindPackageShare('sl_vcu_all'),
            'config',
            'jack_control.yaml'
        ]),
        description='Path to the jack control configuration file'
    )

    log_level_arg = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='Log level (debug, info, warn, error)'
    )

    can_interface_arg = DeclareLaunchArgument(
        'can_interface',
        default_value='can0',
        description='CAN interface name'
    )

    # Jack control node
    jack_control_node = Node(
        package='sl_vcu_all',
        executable='jack_control_node',
        name='jack_control',
        parameters=[
            LaunchConfiguration('config_file'),
            {
                'can_interface': LaunchConfiguration('can_interface')
            }
        ],
        output='screen',
        emulate_tty=True,
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )

    return LaunchDescription([
        # Launch arguments
        config_arg,
        log_level_arg,
        can_interface_arg,
        
        # Nodes
        jack_control_node,
    ])


if __name__ == '__main__':
    generate_launch_description()
