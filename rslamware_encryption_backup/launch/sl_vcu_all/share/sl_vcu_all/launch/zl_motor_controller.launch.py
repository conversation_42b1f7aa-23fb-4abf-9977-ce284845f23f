#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    # Declare launch arguments
    config_arg = DeclareLaunchArgument(
        'config',
        default_value='zl_motor_controller',
        description='Configuration name to use from the YAML file (zl_motor_controller, zl_motor_controller_sim, zl_motor_controller_debug)'
    )

    log_level_arg = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='Log level (debug, info, warn, error)'
    )

    # Get package directory
    pkg_share = FindPackageShare('sl_vcu_all')
    
    # Path to config file
    config_file = PathJoinSubstitution([
        pkg_share,
        'config',
        'zl_motor_controller.yaml'
    ])

    # ZL Motor Controller Node
    zl_motor_controller_node = Node(
        package='sl_vcu_all',
        executable='zl_motor_controller_node',
        name='zl_motor_controller',
        parameters=[LaunchConfiguration('config'), config_file],
        output='screen',
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )

    return LaunchDescription([
        config_arg,
        log_level_arg,
        zl_motor_controller_node
    ])
