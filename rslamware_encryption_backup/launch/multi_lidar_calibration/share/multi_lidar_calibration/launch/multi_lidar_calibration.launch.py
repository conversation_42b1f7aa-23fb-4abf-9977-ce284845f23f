from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node

def generate_launch_description():
    enable_ground_filter_arg = DeclareLaunchArgument(
        'enable_ground_filter',
        default_value='false',
        description='Enable ground filtering for calibration'
    )
    
    master_id_arg = DeclareLaunchArgument(
        'master_id',
        default_value='0',
        description='ID of the master LiDAR'
    )
    
    result_path_arg = DeclareLaunchArgument(
        'result_path',
        default_value='calibration_result.txt',
        description='Path to save calibration result'
    )
    
    multi_lidar_calibration_node = Node(
        package='multi_lidar_calibration',
        executable='multi_lidar_calibration_node',
        name='multi_lidar_calibration_node',
        output='screen',
        parameters=[{
            'enable_ground_filter': LaunchConfiguration('enable_ground_filter'),
            'master_id': LaunchConfiguration('master_id'),
            'result_path': LaunchConfiguration('result_path'),
            'lidar_count': 2,

            'lidar0.id': 0,
            'lidar0.topic': '/lidar_front/scan',
            'lidar0.transform': [
                1.0, 0.0, 0.0, 0.0,  
                0.0, 1.0, 0.0, 0.0,  
                0.0, 0.0, 1.0, 0.0,  
                0.0, 0.0, 0.0, 1.0   
            ],

            'lidar1.id': 1,
            'lidar1.topic': '/lidar_back/scan',
            'lidar1.transform': [
                1.0, 0.0, 0.0, 0.0,  
                0.0, 1.0, 0.0, -0.1,  
                0.0, 0.0, 1.0, 0.0,  
                0.0, 0.0, 0.0, 1.0 
            ]
        }]
    )
    
    return LaunchDescription([
        enable_ground_filter_arg,
        master_id_arg,
        result_path_arg,
        multi_lidar_calibration_node
    ])