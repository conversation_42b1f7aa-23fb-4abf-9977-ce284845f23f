explore_lite_share_explore_lite_config_params:
  /**:
    ros__parameters:
      costmap_topic: map
      costmap_updates_topic: map_updates
      gain_scale: 1.0
      min_frontier_size: 0.3
      orientation_scale: 0.0
      planner_frequency: 0.15
      potential_scale: 3.0
      progress_timeout: 30.0
      return_to_init: false
      robot_base_frame: base_link
      transform_tolerance: 0.3
      visualize: true
explore_lite_share_explore_lite_config_params_costmap:
  explore_node:
    ros__parameters:
      costmap_topic: /global_costmap/costmap
      costmap_updates_topic: /global_costmap/costmap_updates
      gain_scale: 1.0
      min_frontier_size: 0.5
      orientation_scale: 0.0
      planner_frequency: 0.15
      potential_scale: 3.0
      progress_timeout: 30.0
      robot_base_frame: base_link
      transform_tolerance: 0.3
      visualize: true
rslamware_bringup_share_rslamware_bringup_config_laser_filter:
  laser_filter_back:
    ros__parameters:
      filter1:
        name: angle
        params:
          lower_angle: -1.05
          replace_with_nan: true
          upper_angle: 1.05
        type: laser_filters/LaserScanAngularBoundsFilterInPlace
      filter2:
        name: median_spatial
        params:
          window_size: 15
        type: laser_filters/LaserScanMedianSpatialFilter
      filter3:
        name: shadows
        params:
          max_angle: 170.0
          min_angle: 10.0
          neighbors: 0
          window: 1
        type: laser_filters/ScanShadowsFilter
  laser_filter_front:
    ros__parameters:
      filter1:
        name: angle
        params:
          lower_angle: -0.99
          replace_with_nan: true
          upper_angle: 0.99
        type: laser_filters/LaserScanAngularBoundsFilterInPlace
      filter2:
        name: median_spatial
        params:
          window_size: 15
        type: laser_filters/LaserScanMedianSpatialFilter
      filter3:
        name: shadows
        params:
          max_angle: 170.0
          min_angle: 10.0
          neighbors: 0
          window: 1
        type: laser_filters/ScanShadowsFilter
rslamware_bringup_share_rslamware_bringup_config_navigation:
  amcl:
    ros__parameters:
      alpha1: 0.2
      alpha2: 0.2
      alpha3: 0.8
      alpha4: 0.2
      alpha5: 0.1
      base_frame_id: base_link
      beam_skip_distance: 0.5
      beam_skip_error_threshold: 0.9
      beam_skip_threshold: 0.3
      do_beamskip: false
      global_frame_id: map
      lambda_short: 0.1
      laser_likelihood_max_dist: 2.0
      laser_max_range: 100.0
      laser_min_range: -1.0
      laser_model_type: likelihood_field
      max_beams: 60
      max_particles: 2000
      min_particles: 500
      odom_frame_id: odom
      pf_err: 0.05
      pf_z: 0.99
      recovery_alpha_fast: 0.0
      recovery_alpha_slow: 0.0
      resample_interval: 2
      robot_model_type: nav2_amcl::DifferentialMotionModel
      save_pose_rate: 0.5
      scan_topic: fusion_scan
      sigma_hit: 0.2
      tf_broadcast: true
      transform_tolerance: 1.0
      update_min_a: 0.2
      update_min_d: 0.2
      use_sim_time: false
      z_hit: 0.5
      z_max: 0.05
      z_rand: 0.5
      z_short: 0.05
  behavior_server:
    ros__parameters:
      assisted_teleop:
        plugin: nav2_behaviors/AssistedTeleop
      backup:
        plugin: pb_nav2_behaviors/BackUpFreeSpace
      behavior_plugins:
      - spin
      - backup
      - drive_on_heading
      - assisted_teleop
      - wait
      costmap_topic: local_costmap/costmap_raw
      cycle_frequency: 10.0
      drive_on_heading:
        plugin: nav2_behaviors/DriveOnHeading
      footprint_topic: local_costmap/published_footprint
      free_threshold: 5
      global_frame: odom
      max_radius: 0.55
      max_rotational_vel: 0.8
      min_rotational_vel: 0.4
      robot_base_frame: base_link
      rotational_acc_lim: 3.2
      service_name: local_costmap/get_costmap
      simulate_ahead_time: 2.0
      spin:
        plugin: nav2_behaviors/Spin
      transform_tolerance: 0.3
      use_sim_time: false
      visualize: true
      wait:
        plugin: nav2_behaviors/Wait
  bt_navigator:
    ros__parameters:
      bt_loop_duration: 10
      default_server_timeout: 20
      global_frame: map
      odom_topic: /odom
      plugin_lib_names:
      - nav2_compute_path_to_pose_action_bt_node
      - nav2_compute_path_through_poses_action_bt_node
      - nav2_smooth_path_action_bt_node
      - nav2_follow_path_action_bt_node
      - nav2_spin_action_bt_node
      - nav2_wait_action_bt_node
      - nav2_assisted_teleop_action_bt_node
      - nav2_back_up_action_bt_node
      - nav2_drive_on_heading_bt_node
      - nav2_clear_costmap_service_bt_node
      - nav2_is_stuck_condition_bt_node
      - nav2_goal_reached_condition_bt_node
      - nav2_goal_updated_condition_bt_node
      - nav2_globally_updated_goal_condition_bt_node
      - nav2_is_path_valid_condition_bt_node
      - nav2_initial_pose_received_condition_bt_node
      - nav2_reinitialize_global_localization_service_bt_node
      - nav2_rate_controller_bt_node
      - nav2_distance_controller_bt_node
      - nav2_speed_controller_bt_node
      - nav2_truncate_path_action_bt_node
      - nav2_truncate_path_local_action_bt_node
      - nav2_goal_updater_node_bt_node
      - nav2_recovery_node_bt_node
      - nav2_pipeline_sequence_bt_node
      - nav2_round_robin_node_bt_node
      - nav2_transform_available_condition_bt_node
      - nav2_time_expired_condition_bt_node
      - nav2_path_expiring_timer_condition
      - nav2_distance_traveled_condition_bt_node
      - nav2_single_trigger_bt_node
      - nav2_goal_updated_controller_bt_node
      - nav2_is_battery_low_condition_bt_node
      - nav2_navigate_through_poses_action_bt_node
      - nav2_navigate_to_pose_action_bt_node
      - nav2_remove_passed_goals_action_bt_node
      - nav2_planner_selector_bt_node
      - nav2_controller_selector_bt_node
      - nav2_goal_checker_selector_bt_node
      - nav2_controller_cancel_bt_node
      - nav2_path_longer_on_approach_bt_node
      - nav2_wait_cancel_bt_node
      - nav2_spin_cancel_bt_node
      - nav2_back_up_cancel_bt_node
      - nav2_assisted_teleop_cancel_bt_node
      - nav2_drive_on_heading_cancel_bt_node
      - nav2_is_battery_charging_condition_bt_node
      - nav2_path_monitor_bt_node
      - nav2_path_evaluator_bt_node
      - nav2_spin_to_goal_action_bt_node
      robot_base_frame: base_link
      transform_tolerance: 0.3
      use_sim_time: false
      wait_for_service_timeout: 1000
  bt_navigator_navigate_through_poses_rclcpp_node:
    ros__parameters:
      use_sim_time: false
  bt_navigator_navigate_to_pose_rclcpp_node:
    ros__parameters:
      use_sim_time: false
  controller_server:
    ros__parameters:
      FollowPath:
        BaseObstacle.scale: 0.0
        GoalAlign.forward_point_distance: 0.1
        GoalAlign.scale: 24.0
        GoalDist.scale: 24.0
        ObstacleFootprint.scale: 0.5
        Oscillation.scale: 1.0
        PathAlign.forward_point_distance: 0.1
        PathAlign.scale: 32.0
        PathDist.scale: 40.0
        PreferForward.scale: 0.1
        RotateToGoal.lookahead_time: -1.0
        RotateToGoal.scale: 32.0
        RotateToGoal.slowing_factor: 5.0
        acc_lim_theta: 3.0
        acc_lim_x: 0.3
        acc_lim_y: 0.0
        angular_disengage_threshold: 0.5
        angular_dist_threshold: 1.0
        angular_granularity: 0.025
        critics:
        - RotateToGoal
        - Oscillation
        - BaseObstacle
        - ObstacleFootprint
        - GoalAlign
        - PathAlign
        - PathDist
        - GoalDist
        - PreferForward
        debug_trajectory_details: true
        decel_lim_theta: -3.0
        decel_lim_x: -1.0
        decel_lim_y: 0.0
        forward_sampling_distance: 0.5
        linear_granularity: 0.05
        max_angular_accel: 3.2
        max_speed_xy: 1.0
        max_vel_theta: 1.0
        max_vel_x: 1.0
        max_vel_y: 0.0
        min_speed_theta: 0.0
        min_speed_xy: 0.0
        min_vel_x: 0.0
        min_vel_y: 0.0
        plugin: nav2_rotation_shim_controller::RotationShimController
        primary_controller: dwb_core::DWBLocalPlanner
        rot_stopped_velocity: 0.01
        rotate_to_goal_heading: false
        rotate_to_heading_angular_vel: 0.8
        short_circuit_trajectory_evaluation: true
        sim_time: 1.2
        simulate_ahead_time: 1.0
        speed_reduce_enabled: true
        speed_reduce_min_threshold: 0.8
        stateful: true
        trans_stopped_velocity: 0.01
        transform_tolerance: 0.3
        use_differential_drive: true
        vtheta_samples: 20
        vx_samples: 20
        vy_samples: 0
        xy_goal_tolerance: 0.1
      controller_frequency: 30.0
      controller_plugins:
      - FollowPath
      failure_tolerance: 0.3
      general_goal_checker:
        plugin: nav2_controller::SimpleGoalChecker
        stateful: false
        xy_goal_tolerance: 0.1
        yaw_goal_tolerance: 3.14
      goal_checker_plugins:
      - general_goal_checker
      min_theta_velocity_threshold: 0.001
      min_x_velocity_threshold: 0.001
      min_y_velocity_threshold: 0.0
      progress_checker:
        movement_time_allowance: 6.0
        plugin: nav2_controller::PoseProgressChecker
        required_movement_radius: 0.05
      progress_checker_plugin: progress_checker
      transform_tolerance: 0.3
      use_realtime_priority: true
      use_sim_time: false
  gl_pose_sampler:
    ros__parameters:
      average_sdf_delta_th: 0.01
      gradient_square_th: 0.01
      key_scans_num: 3
      keypoints_min_dist_from_map: 0.5
      laser_frame: base_link
      random_samples_num: 20
      scan_name: fusion_scan
  global_costmap:
    global_costmap:
      ros__parameters:
        always_send_full_costmap: true
        denoise_layer:
          enabled: true
          minimal_group_size: 3
          plugin: nav2_costmap_2d::DenoiseLayer
        footprint: '[[-0.39, -0.24], [-0.39, 0.24], [0.39, 0.24], [0.39, -0.24]]'
        global_frame: map
        inflation_layer:
          cost_scaling_factor: 3.0
          inflation_radius: 0.6
          plugin: nav2_costmap_2d::InflationLayer
        keepout_filter:
          enabled: true
          filter_info_topic: /costmap_filter_info
          plugin: nav2_costmap_2d::KeepoutFilter
          transform_tolerance: 0.3
        map_topic: /map
        obstacle_layer:
          depth_points:
            clearing: false
            data_type: PointCloud2
            marking: true
            max_obstacle_height: 1.3
            observation_persistence: 1.0
            obstacle_max_range: 2.0
            obstacle_min_range: 0.0
            raytrace_max_range: 2.0
            raytrace_min_range: 0.0
            topic: /depthcam/processed_cloud
          enabled: true
          laser_points_filter_enabled: true
          observation_sources: scan depth_points
          plugin: nav2_costmap_2d::ObstacleLayer
          scan:
            clearing: true
            data_type: LaserScan
            marking: true
            max_obstacle_height: 2.0
            observation_persistence: 0.5
            obstacle_max_range: 3.0
            raytrace_max_range: 3.0
            topic: /fusion_scan
        plugins:
        - static_layer
        - voxel_layer
        - obstacle_layer
        - keepout_filter
        - denoise_layer
        - inflation_layer
        publish_frequency: 4.0
        resolution: 0.05
        robot_base_frame: base_link
        static_layer:
          map_subscribe_transient_local: true
          plugin: nav2_costmap_2d::StaticLayer
        static_map: false
        track_unknown_space: true
        transform_tolerance: 0.3
        update_frequency: 4.0
        use_sim_time: false
        voxel_layer:
          depth_points:
            clearing: true
            data_type: PointCloud2
            marking: true
            max_obstacle_height: 1.3
            observation_persistence: 0.2
            obstacle_max_range: 2.0
            obstacle_min_range: 0.0
            raytrace_max_range: 2.0
            raytrace_min_range: 0.0
            topic: /depthcam/processed_cloud
          enabled: false
          mark_threshold: 4
          max_obstacle_height: 1.3
          observation_sources: depth_points
          origin_z: 0.0
          plugin: nav2_costmap_2d::VoxelLayer
          publish_voxel_map: true
          z_resolution: 0.05
          z_voxels: 16
  local_costmap:
    local_costmap:
      ros__parameters:
        always_send_full_costmap: true
        denoise_layer:
          enabled: true
          minimal_group_size: 3
          plugin: nav2_costmap_2d::DenoiseLayer
        footprint: '[[-0.39, -0.245], [-0.39, 0.245], [0.39, 0.245], [0.39, -0.245]]'
        global_frame: odom
        height: 4
        inflation_layer:
          cost_scaling_factor: 3.0
          inflation_radius: 0.4
          plugin: nav2_costmap_2d::InflationLayer
        keepout_filter:
          enabled: true
          filter_info_topic: /costmap_filter_info
          plugin: nav2_costmap_2d::KeepoutFilter
          transform_tolerance: 0.3
        obstacle_layer:
          depth_points:
            clearing: false
            data_type: PointCloud2
            marking: true
            max_obstacle_height: 1.3
            observation_persistence: 1.0
            obstacle_max_range: 2.0
            obstacle_min_range: 0.0
            raytrace_max_range: 2.0
            raytrace_min_range: 0.0
            topic: /depthcam/processed_cloud
          enabled: true
          laser_points_filter_enabled: true
          observation_sources: scan depth_points
          plugin: nav2_costmap_2d::ObstacleLayer
          scan:
            clearing: true
            data_type: LaserScan
            marking: true
            max_obstacle_height: 1.2
            observation_persistence: 0.5
            obstacle_max_range: 2.0
            raytrace_max_range: 2.0
            topic: /fusion_scan
        plugins:
        - obstacle_layer
        - voxel_layer
        - keepout_filter
        - denoise_layer
        - inflation_layer
        publish_frequency: 30.0
        resolution: 0.05
        robot_base_frame: base_link
        rolling_window: true
        static_layer:
          map_subscribe_transient_local: true
          plugin: nav2_costmap_2d::StaticLayer
        transform_tolerance: 0.3
        update_frequency: 30.0
        use_sim_time: false
        voxel_layer:
          depth_points:
            clearing: true
            data_type: PointCloud2
            marking: true
            max_obstacle_height: 1.3
            observation_persistence: 0.2
            obstacle_max_range: 2.0
            obstacle_min_range: 0.0
            raytrace_max_range: 2.0
            raytrace_min_range: 0.0
            topic: /depthcam/processed_cloud
          enabled: false
          mark_threshold: 4
          max_obstacle_height: 1.3
          observation_sources: depth_points
          origin_z: 0.0
          plugin: nav2_costmap_2d::VoxelLayer
          publish_voxel_map: true
          z_resolution: 0.05
          z_voxels: 16
        width: 4
  map_saver:
    ros__parameters:
      free_thresh_default: 0.25
      map_subscribe_transient_local: true
      occupied_thresh_default: 0.65
      save_map_timeout: 5.0
      use_sim_time: false
  map_server:
    ros__parameters:
      use_sim_time: false
      yaml_filename: map.yaml
  mcl:
    ros__parameters:
      estimate_reliability: false
      laser_frame: base_link
      localization_hz: 20.0
      odom_noise_ddm:
      - 0.2
      - 0.1
      - 0.1
      - 0.3
      particle_num: 1000
      reject_unknown_scan: true
      resample_thresholds:
      - 0.1
      - 0.1
      - 0.1
      - 0.017
      - -99999.0
      scan_name: fusion_scan
      scan_step: 1
      use_augmented_mcl: true
      use_gl_pose_sampler: false
      var_hit: 0.05
      z_hit: 0.95
      z_max: 0.01
      z_rand: 0.01
      z_short: 0.1
  planner_server:
    ros__parameters:
      GridBased:
        allow_unknown: false
        angle_tolerance: 0.1
        cost_travel_multiplier: 3.0
        downsample_costmap: false
        downsampling_factor: 2
        goal_checker_name: simple_goal_checker
        heuristic_downweight: 1.0
        heuristic_type: euclidean
        interpolation_resolution: 0.1
        max_iterations: 100000
        max_on_approach_iterations: 1000
        plugin: nav2_smac_planner/SmacPlanner2D
        smooth_path: true
        tolerance: 0.2
        xy_goal_tolerance: 0.25
      expected_planner_frequency: 5.0
      planner_plugins:
      - GridBased
      use_sim_time: false
  robot_state_publisher:
    ros__parameters:
      use_sim_time: false
  smoother_server:
    ros__parameters:
      simple_smoother:
        cost_threshold: 100.0
        do_refinement: true
        interpolation_resolution: 0.1
        max_its: 1000
        plugin: nav2_smoother::SimpleSmoother
        tolerance: 1.0e-10
      smoother_plugins:
      - simple_smoother
      use_sim_time: false
  velocity_smoother:
    ros__parameters:
      deadband_velocity:
      - 0.0
      - 0.0
      - 0.0
      feedback: OPEN_LOOP
      max_accel:
      - 0.5
      - 0.0
      - 5.0
      max_decel:
      - -1.0
      - 0.0
      - -5.0
      max_velocity:
      - 1.0
      - 0.0
      - 1.0
      min_velocity:
      - -0.2
      - 0.0
      - -1.0
      odom_duration: 0.1
      odom_topic: odom
      scale_velocities: true
      smoothing_frequency: 30.0
      use_sim_time: false
      velocity_timeout: 0.5
  waypoint_follower:
    ros__parameters:
      loop_rate: 20
      stop_on_failure: false
      use_sim_time: false
      wait_at_waypoint:
        enabled: true
        plugin: nav2_waypoint_follower::WaitAtWaypoint
        waypoint_pause_duration: 200
      waypoint_task_executor_plugin: wait_at_waypoint
rslamware_bringup_share_rslamware_bringup_config_rplidar:
  rplidar_node_back:
    ros__parameters:
      angle_compensate: true
      channel_type: udp
      frame_id: rplidar_back
      inverted: false
      min_distance: 0.05
      scan_frequency: 15.0
      scan_mode: UltraDense
      udp_ip: *************
      udp_port: 8089
  rplidar_node_front:
    ros__parameters:
      angle_compensate: true
      channel_type: udp
      frame_id: rplidar_front
      inverted: false
      min_distance: 0.05
      scan_frequency: 15.0
      scan_mode: UltraDense
      udp_ip: ************
      udp_port: 8089
rslamware_bringup_share_rslamware_bringup_config_rslamware:
  robot_monitor_node:
    ros__parameters:
      disk_usage_warning_threshold: 90.0
      enable_disk_monitoring: true
      enable_temperature_monitoring: true
      health_monitor_period: 1.0
      lidar_topic: /fusion_scan
      system_monitor_period: 60.0
      temperature_warning_threshold: 80.0
  stcm_manager_node:
    ros__parameters:
      keep_mask_topic: /keepout_filter_mask
      map_server_topic: /map
      map_storage_path: /home/<USER>/maps/
rslamware_bringup_share_rslamware_bringup_config_topics:
  topics:
    ros__parameters:
      back_laser_scan: lidar_back/scan
      cmd: cmd_vel
      filtered_back_laser_scan: lidar_back/scan_filtered
      filtered_front_laser_scan: lidar_front/scan_filtered
      front_laser_scan: lidar_front/scan
      fusion_scan: fusion_scan
      undistortion_back_laser_scan: lidar_back/scan_undistortion
      undistortion_front_laser_scan: lidar_front/scan_undistortion
sl_chassis_ros_share_sl_chassis_ros_config_ekf:
  ekf_filter_node:
    ros__parameters:
      acceleration_gains:
      - 0.8
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.9
      acceleration_limits:
      - 1.3
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 3.4
      base_link_frame: base_link
      control_config:
      - true
      - false
      - false
      - false
      - false
      - true
      control_timeout: 0.2
      debug: false
      debug_out_file: /path/to/debug/file.txt
      deceleration_gains:
      - 1.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1.0
      deceleration_limits:
      - 1.3
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 4.5
      frequency: 30.0
      imu0: example/imu
      imu0_config:
      - false
      - false
      - false
      - true
      - true
      - true
      - false
      - false
      - false
      - true
      - true
      - true
      - true
      - true
      - true
      imu0_differential: false
      imu0_linear_acceleration_rejection_threshold: 0.8
      imu0_pose_rejection_threshold: 0.8
      imu0_queue_size: 5
      imu0_relative: true
      imu0_remove_gravitational_acceleration: true
      imu0_twist_rejection_threshold: 0.8
      initial_estimate_covariance:
      - 1e-9
      - 1e-9
      - 1e-9
      - 1e-9
      - 1e-9
      - 1e-9
      - 1e-9
      - 1e-9
      - 1e-9
      - 1e-9
      - 1e-9
      - 1e-9
      - 1e-9
      - 1e-9
      - 1e-9
      map_frame: map
      odom0: example/odom
      odom0_config:
      - true
      - true
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - true
      - false
      - false
      - false
      odom0_differential: false
      odom0_pose_rejection_threshold: 5.0
      odom0_pose_use_child_frame: false
      odom0_queue_size: 2
      odom0_relative: false
      odom0_twist_rejection_threshold: 1.0
      odom1: example/odom2
      odom1_config:
      - false
      - false
      - true
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - true
      - false
      - false
      - false
      odom1_differential: false
      odom1_pose_rejection_threshold: 2.0
      odom1_queue_size: 2
      odom1_relative: true
      odom1_twist_rejection_threshold: 0.2
      odom_frame: odom
      permit_corrected_publication: false
      pose0: example/pose
      pose0_config:
      - true
      - true
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      pose0_differential: true
      pose0_queue_size: 5
      pose0_rejection_threshold: 2.0
      pose0_relative: false
      print_diagnostics: true
      process_noise_covariance:
      - 0.05
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.05
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.06
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.03
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.03
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.06
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.025
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.025
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.04
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.02
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.015
      publish_acceleration: false
      publish_tf: true
      sensor_timeout: 0.1
      stamped_control: false
      transform_time_offset: 0.0
      transform_timeout: 0.0
      twist0: example/twist
      twist0_config:
      - false
      - false
      - false
      - false
      - false
      - false
      - true
      - true
      - true
      - false
      - false
      - false
      - false
      - false
      - false
      twist0_queue_size: 3
      twist0_rejection_threshold: 2.0
      two_d_mode: false
      use_control: true
      world_frame: odom
sl_chassis_ros_share_sl_chassis_ros_config_imu_filter_complementary:
  imu_filter:
    ros__parameters:
      bias_alpha: 0.01
      constant_dt: 0.0
      do_adaptive_gain: true
      do_bias_estimation: true
      fixed_frame: odom
      gain_acc: 0.01
      gain_mag: 0.01
      publish_debug_topics: true
      publish_tf: true
      qos_overrides:
        /imu/data_raw:
          subscription:
            depth: 10
            durability: volatile
            history: keep_last
            reliability: reliable
        /imu/mag:
          subscription:
            depth: 10
            durability: volatile
            history: keep_last
            reliability: reliable
      reverse_tf: false
      use_mag: false
sl_chassis_ros_share_sl_chassis_ros_config_imu_filter_madgwick:
  imu_filter:
    ros__parameters:
      constant_dt: 0.0
      fixed_frame: odom
      gain: 0.1
      mag_bias_x: 0.0
      mag_bias_y: 0.0
      mag_bias_z: 0.0
      orientation_stddev: 0.0
      publish_debug_topics: true
      publish_tf: true
      reverse_tf: false
      stateless: false
      use_mag: false
      world_frame: enu
      zeta: 0.1
sl_chassis_ros_share_sl_chassis_ros_config_robot_localization_ekf:
  ekf_filter_node:
    ros__parameters:
      acceleration_gains:
      - 0.8
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.9
      acceleration_limits:
      - 1.3
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 3.4
      base_link_frame: base_link
      control_config:
      - true
      - false
      - false
      - false
      - false
      - true
      control_timeout: 0.2
      debug: false
      debug_out_file: /home/<USER>/works/agv_ros/sl_chassis_ws/robot_localization_debug.txt
      deceleration_gains:
      - 1.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1.0
      deceleration_limits:
      - 1.3
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 4.5
      frequency: 50.0
      imu0: sl_chassis_imu/imu_raw
      imu0_config:
      - false
      - false
      - false
      - false
      - false
      - true
      - false
      - false
      - false
      - false
      - false
      - true
      - false
      - false
      - false
      imu0_differential: false
      imu0_queue_size: 10
      imu0_relative: true
      imu0_remove_gravitational_acceleration: true
      map_frame: map
      odom0: sl_chassis_base/odom
      odom0_config:
      - true
      - true
      - false
      - false
      - false
      - true
      - true
      - false
      - false
      - false
      - false
      - true
      - false
      - false
      - false
      odom0_differential: false
      odom0_pose_use_child_frame: false
      odom0_queue_size: 5
      odom0_relative: true
      odom_frame: odom
      permit_corrected_publication: false
      print_diagnostics: true
      process_noise_covariance:
      - 0.05
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.05
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.06
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.03
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.03
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.06
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.025
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.025
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.04
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.02
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.015
      publish_acceleration: false
      publish_tf: true
      sensor_timeout: 0.1
      stamped_control: false
      transform_time_offset: 0.0
      transform_timeout: 0.0
      two_d_mode: true
      use_control: true
      world_frame: odom
sl_vcu_all_share_sl_vcu_all_config_battery_monitor:
  /**:
    ros__parameters:
      battery_status_publish_rate_ms: 20
      battery_status_topic: battery_status
      battery_topic: battery_state
      can_id_battery_info: 256
      can_id_battery_status: 257
      can_interface: can0
      charge_event_code: 63
      input_device_path: /dev/input/event2
      manual_charge_event_code: 62
      min_send_interval_ms: 20
      poll_timeout_ms: 100
      publish_rate_ms: 1000
      request_interval_ms: 1000
sl_vcu_all_share_sl_vcu_all_config_bumper_sensor:
  bumper_sensor:
    ros__parameters:
      back_bumper_code: 60
      bumper_topic: bumper_state
      front_bumper_code: 59
      input_device_path: /dev/input/event2
      poll_timeout_ms: 1000
      publish_rate_ms: 20
      triggered_value: 1
sl_vcu_all_share_sl_vcu_all_config_ekf:
  ekf_filter_node:
    ros__parameters:
      acceleration_gains:
      - 0.8
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.9
      acceleration_limits:
      - 1.3
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 3.4
      base_link_frame: base_link
      control_config:
      - true
      - false
      - false
      - false
      - false
      - true
      control_timeout: 0.2
      debug: false
      debug_out_file: /path/to/debug/file.txt
      deceleration_gains:
      - 1.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1.0
      deceleration_limits:
      - 1.3
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 4.5
      frequency: 30.0
      imu0: example/imu
      imu0_config:
      - false
      - false
      - false
      - true
      - true
      - true
      - false
      - false
      - false
      - true
      - true
      - true
      - true
      - true
      - true
      imu0_differential: false
      imu0_linear_acceleration_rejection_threshold: 0.8
      imu0_nodelay: false
      imu0_pose_rejection_threshold: 0.8
      imu0_queue_size: 5
      imu0_relative: true
      imu0_remove_gravitational_acceleration: true
      imu0_twist_rejection_threshold: 0.8
      initial_estimate_covariance:
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      map_frame: map
      odom0: example/odom
      odom0_config:
      - true
      - true
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - true
      - false
      - false
      - false
      odom0_differential: false
      odom0_nodelay: false
      odom0_pose_rejection_threshold: 5.0
      odom0_queue_size: 2
      odom0_relative: false
      odom0_twist_rejection_threshold: 1.0
      odom1: example/odom2
      odom1_config:
      - false
      - false
      - true
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - true
      - false
      - false
      - false
      odom1_differential: false
      odom1_nodelay: false
      odom1_pose_rejection_threshold: 2.0
      odom1_queue_size: 2
      odom1_relative: true
      odom1_twist_rejection_threshold: 0.2
      odom_frame: odom
      permit_corrected_publication: false
      pose0: example/pose
      pose0_config:
      - true
      - true
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      - false
      pose0_differential: true
      pose0_nodelay: false
      pose0_queue_size: 5
      pose0_rejection_threshold: 2.0
      pose0_relative: false
      print_diagnostics: true
      process_noise_covariance:
      - 0.05
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.05
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.06
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.03
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.03
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.06
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.025
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.025
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.04
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.02
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.015
      publish_acceleration: false
      publish_tf: true
      sensor_timeout: 0.1
      stamped_control: false
      transform_time_offset: 0.0
      transform_timeout: 0.0
      twist0: example/twist
      twist0_config:
      - false
      - false
      - false
      - false
      - false
      - false
      - true
      - true
      - true
      - false
      - false
      - false
      - false
      - false
      - false
      twist0_nodelay: false
      twist0_queue_size: 3
      twist0_rejection_threshold: 2.0
      two_d_mode: false
      use_control: true
      world_frame: odom
sl_vcu_all_share_sl_vcu_all_config_imu_sensor:
  imu_sensor:
    ros__parameters:
      accel_device_path: /dev/iio:device2
      bias_calculation_time: 10.0
      bias_update_threshold: 0.01
      bias_update_time: 5.0
      child_frame_id: imu_link
      cmd_vel_timeout: 2.0
      cmd_vel_topic: cmd_vel
      gyro_device_path: /dev/iio:device1
      imu_filtered_topic: sl_vcu_all/imu_data_filtered
      imu_frame_id: imu_link
      imu_sensor_acc_sensitivity: 4
      imu_sensor_gyro_sensitivity: 2000
      imu_topic: sl_vcu_all/imu_data_raw
      initial_bias_offset: 0.1
      parent_frame_id: base_link
      poll_timeout: 1000
      publish_period_ms: 10
      publish_tf: false
      timestamp_sync_tolerance_ns: 10000000
sl_vcu_all_share_sl_vcu_all_config_jack_control:
  jack_control:
    ros__parameters:
      auto_detect_base_on_start: true
      base_stage_timeout_s: 45
      can_interface: can0
      control_cycle_ms: 50
      default_speed: 1000
      detection_timeout_s: 30
      down_stage_timeout_s: 90
      enable: true
      heartbeat_period_ms: 100
      max_position: 26000000
      max_speed: 3000
      min_position: 0
      min_send_interval_ms: 5
      movement_check_tolerance: 100
      movement_start_delay_s: 3
      position_timeout_s: 60
      position_tolerance: 1000
      response_timeout_ms: 1000
      status_update_delay_s: 2
      up_stage_timeout_s: 90
sl_vcu_all_share_sl_vcu_all_config_led_control:
  led_display_control:
    ros__parameters:
      auto_start_effects: true
      channel_0:
        enabled: true
        first_part_leds: 5
        num_leds: 10
        second_part_leds: 5
        spi_device: /dev/spidev0.0
        spi_speed: 7080000
      channel_1:
        enabled: true
        first_part_leds: 5
        num_leds: 10
        second_part_leds: 5
        spi_device: /dev/spidev3.0
        spi_speed: 7080000
      debug_mode: false
      default_blue_brightness: 255
      default_frequency: 1.0
      default_green_brightness: 255
      default_marquee_direction: true
      default_on_time_duty: 0.5
      default_red_brightness: 255
      default_speed: 10.0
      num_channels: 2
sl_vcu_all_share_sl_vcu_all_config_robot_localization_ekf:
  ekf_filter_node:
    ros__parameters:
      acceleration_gains:
      - 0.8
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.9
      acceleration_limits:
      - 1.3
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 3.4
      base_link_frame: base_link
      control_config:
      - true
      - false
      - false
      - false
      - false
      - true
      control_timeout: 0.2
      debug: false
      debug_out_file: /home/<USER>/works/sl_vcu_ws/ekf_file.txt
      deceleration_gains:
      - 1.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1.0
      deceleration_limits:
      - 1.3
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 4.5
      frequency: 40.0
      imu0: sl_vcu_all/imu_data_filtered
      imu0_config:
      - false
      - false
      - false
      - false
      - false
      - true
      - false
      - false
      - false
      - false
      - false
      - true
      - false
      - false
      - false
      imu0_differential: false
      imu0_nodelay: false
      imu0_queue_size: 10
      imu0_relative: true
      imu0_remove_gravitational_acceleration: true
      initial_estimate_covariance:
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 1e-9
      map_frame: map
      odom0: sl_vcu_all/odom
      odom0_config:
      - true
      - true
      - false
      - false
      - false
      - true
      - true
      - false
      - false
      - false
      - false
      - true
      - false
      - false
      - false
      odom0_differential: false
      odom0_nodelay: false
      odom0_queue_size: 5
      odom0_relative: true
      odom_frame: odom
      permit_corrected_publication: false
      print_diagnostics: false
      process_noise_covariance:
      - 0.05
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.05
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.06
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.03
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.03
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.06
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.025
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.025
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.04
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.02
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.01
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.015
      publish_acceleration: false
      publish_tf: true
      sensor_timeout: 1.0
      stamped_control: false
      transform_time_offset: 0.0
      transform_timeout: 0.0
      two_d_mode: true
      use_control: true
      world_frame: odom
sl_vcu_all_share_sl_vcu_all_config_zl_motor_controller:
  zl_motor_controller:
    ros__parameters:
      add_filter_service: add_can_filter
      base_frame_id: base_link
      bumper_timeout_ms: 5000
      bumper_topic: bumper_state
      can_id_rx: 1409
      can_id_tx: 1537
      can_interface: can0
      can_rx_topic: ''
      can_tx_topic: can_tx
      check_dispatcher_node: false
      check_status_service: check_node_status
      cmd_vel_timeout_ms: 500
      cmd_vel_topic: cmd_vel
      control_cycle_ms: 25
      encoder_resolution: 16384.0
      filtered_odom_topic: ''
      gear_ratio: 1.0
      min_send_interval_ms: 2
      motor_info_topic: motor_info
      odom_frame_id: odom
      odom_topic: odom
      print_status_out: false
      publish_motor_info: false
      publish_tf: true
      sdo_response_timeout_ms: 100
      status_update_cycle_ms: 1000
      use_sockcan_direct: true
      wheel_diameter_left: 0.1388
      wheel_diameter_right: 0.14
      wheel_separation: 0.39
slam_toolbox_share_slam_toolbox_config_mapper_params_lifelong:
  slam_toolbox:
    ros__parameters:
      angle_variance_penalty: 0.1225
      base_frame: base_link
      ceres_dogleg_type: TRADITIONAL_DOGLEG
      ceres_linear_solver: SPARSE_NORMAL_CHOLESKY
      ceres_loss_function: None
      ceres_preconditioner: SCHUR_JACOBI
      ceres_trust_strategy: LEVENBERG_MARQUARDT
      coarse_angle_resolution: 0.0349
      coarse_search_angle_offset: 0.52
      correlation_search_space_dimension: 0.3
      correlation_search_space_resolution: 0.01
      correlation_search_space_smear_deviation: 0.03
      debug_logging: false
      distance_variance_penalty: 0.09
      do_loop_closing: true
      fine_search_angle_offset: 0.00349
      lifelong_candidates_scale: 0.03
      lifelong_constraint_multiplier: 0.08
      lifelong_iou_match: 0.85
      lifelong_minimum_score: 0.1
      lifelong_nearby_penalty: 0.001
      lifelong_node_removal_score: 0.04
      lifelong_overlap_score_scale: 0.06
      lifelong_search_use_tree: false
      link_match_minimum_response_fine: 0.6
      link_scan_maximum_distance: 10.0
      loop_match_maximum_variance_coarse: 0.16
      loop_match_minimum_chain_size: 10
      loop_match_minimum_response_coarse: 0.3
      loop_match_minimum_response_fine: 0.8
      loop_search_maximum_distance: 8.0
      loop_search_space_dimension: 8.0
      loop_search_space_resolution: 0.05
      loop_search_space_smear_deviation: 0.03
      map_frame: map
      map_update_interval: 0.5
      max_laser_range: 20.0
      min_laser_range: 0.0
      min_pass_through: 2
      minimum_angle_penalty: 0.7
      minimum_distance_penalty: 0.4
      minimum_time_interval: 0.5
      minimum_travel_distance: 0.2
      minimum_travel_heading: 0.174
      mode: mapping
      occupancy_threshold: 0.1
      odom_frame: odom
      resolution: 0.05
      scan_buffer_maximum_scan_distance: 20.0
      scan_buffer_size: 70
      scan_topic: /fused_scan
      single_scan_topic: /fusion_scan
      solver_plugin: solver_plugins::CeresSolver
      stack_size_to_use: 40000000
      tf_buffer_duration: 10.0
      throttle_scans: 1
      transform_publish_period: 0.02
      transform_timeout: 0.2
      use_fusion_scan: false
      use_map_saver: true
      use_response_expansion: false
      use_scan_barycenter: true
      use_scan_matching: true
slam_toolbox_share_slam_toolbox_config_mapper_params_localization:
  slam_toolbox:
    ros__parameters:
      angle_variance_penalty: 0.1225
      base_frame: base_link
      ceres_dogleg_type: TRADITIONAL_DOGLEG
      ceres_linear_solver: SPARSE_NORMAL_CHOLESKY
      ceres_loss_function: None
      ceres_preconditioner: SCHUR_JACOBI
      ceres_trust_strategy: LEVENBERG_MARQUARDT
      coarse_angle_resolution: 0.0349
      coarse_search_angle_offset: 0.52
      correlation_search_space_dimension: 0.3
      correlation_search_space_resolution: 0.01
      correlation_search_space_smear_deviation: 0.03
      debug_logging: false
      distance_variance_penalty: 0.09
      do_loop_closing: true
      fine_search_angle_offset: 0.00349
      link_match_minimum_response_fine: 0.6
      link_scan_maximum_distance: 10.0
      loop_match_maximum_variance_coarse: 0.16
      loop_match_minimum_chain_size: 10
      loop_match_minimum_response_coarse: 0.3
      loop_match_minimum_response_fine: 0.8
      loop_search_maximum_distance: 8.0
      loop_search_space_dimension: 8.0
      loop_search_space_resolution: 0.05
      loop_search_space_smear_deviation: 0.03
      map_frame: map
      map_update_interval: 0.5
      max_laser_range: 20.0
      min_laser_range: 0.0
      min_pass_through: 2
      minimum_angle_penalty: 0.7
      minimum_distance_penalty: 0.4
      minimum_time_interval: 0.5
      minimum_travel_distance: 0.2
      minimum_travel_heading: 0.174
      mode: mapping
      occupancy_threshold: 0.1
      odom_frame: odom
      resolution: 0.05
      scan_buffer_maximum_scan_distance: 20.0
      scan_buffer_size: 70
      scan_topic: /fused_scan
      single_scan_topic: /fusion_scan
      solver_plugin: solver_plugins::CeresSolver
      stack_size_to_use: 40000000
      tf_buffer_duration: 30.0
      throttle_scans: 1
      transform_publish_period: 0.02
      transform_timeout: 0.2
      use_fusion_scan: false
      use_response_expansion: false
      use_scan_barycenter: true
      use_scan_matching: true
slam_toolbox_share_slam_toolbox_config_mapper_params_online_async:
  slam_toolbox:
    ros__parameters:
      angle_variance_penalty: 0.1225
      base_frame: base_link
      ceres_dogleg_type: TRADITIONAL_DOGLEG
      ceres_linear_solver: SPARSE_NORMAL_CHOLESKY
      ceres_loss_function: None
      ceres_preconditioner: SCHUR_JACOBI
      ceres_trust_strategy: LEVENBERG_MARQUARDT
      coarse_angle_resolution: 0.0349
      coarse_search_angle_offset: 0.52
      correlation_search_space_dimension: 0.3
      correlation_search_space_resolution: 0.01
      correlation_search_space_smear_deviation: 0.03
      debug_logging: false
      distance_variance_penalty: 0.09
      do_loop_closing: true
      enable_interactive_mode: true
      fine_search_angle_offset: 0.00349
      link_match_minimum_response_fine: 0.6
      link_scan_maximum_distance: 10.0
      loop_match_maximum_variance_coarse: 0.16
      loop_match_minimum_chain_size: 10
      loop_match_minimum_response_coarse: 0.3
      loop_match_minimum_response_fine: 0.8
      loop_search_maximum_distance: 8.0
      loop_search_space_dimension: 8.0
      loop_search_space_resolution: 0.05
      loop_search_space_smear_deviation: 0.03
      map_frame: map
      map_update_interval: 0.5
      max_laser_range: 20.0
      min_laser_range: 0.0
      min_pass_through: 2
      minimum_angle_penalty: 0.7
      minimum_distance_penalty: 0.4
      minimum_time_interval: 0.5
      minimum_travel_distance: 0.2
      minimum_travel_heading: 0.174
      mode: mapping
      occupancy_threshold: 0.1
      odom_frame: odom
      resolution: 0.05
      scan_buffer_maximum_scan_distance: 20.0
      scan_buffer_size: 70
      scan_topic: /fused_scan
      single_scan_topic: /fusion_scan
      solver_plugin: solver_plugins::CeresSolver
      stack_size_to_use: 40000000
      tf_buffer_duration: 30.0
      throttle_scans: 1
      transform_publish_period: 0.02
      transform_timeout: 0.2
      use_fusion_scan: false
      use_map_saver: true
      use_response_expansion: false
      use_scan_barycenter: true
      use_scan_matching: true
slam_toolbox_share_slam_toolbox_config_mapper_params_online_sync:
  slam_toolbox:
    ros__parameters:
      angle_variance_penalty: 0.1225
      base_frame: base_link
      ceres_dogleg_type: TRADITIONAL_DOGLEG
      ceres_linear_solver: SPARSE_NORMAL_CHOLESKY
      ceres_loss_function: None
      ceres_preconditioner: SCHUR_JACOBI
      ceres_trust_strategy: LEVENBERG_MARQUARDT
      coarse_angle_resolution: 0.0349
      coarse_search_angle_offset: 0.52
      correlation_search_space_dimension: 0.3
      correlation_search_space_resolution: 0.01
      correlation_search_space_smear_deviation: 0.03
      debug_logging: false
      distance_variance_penalty: 0.09
      do_loop_closing: true
      enable_interactive_mode: true
      fine_search_angle_offset: 0.00349
      link_match_minimum_response_fine: 0.6
      link_scan_maximum_distance: 10.0
      loop_match_maximum_variance_coarse: 0.16
      loop_match_minimum_chain_size: 10
      loop_match_minimum_response_coarse: 0.3
      loop_match_minimum_response_fine: 0.8
      loop_search_maximum_distance: 8.0
      loop_search_space_dimension: 8.0
      loop_search_space_resolution: 0.05
      loop_search_space_smear_deviation: 0.03
      map_frame: map
      map_update_interval: 0.5
      max_laser_range: 20.0
      min_laser_range: 0.0
      min_pass_through: 2
      minimum_angle_penalty: 0.7
      minimum_distance_penalty: 0.4
      minimum_time_interval: 0.1
      minimum_travel_distance: 0.2
      minimum_travel_heading: 0.174
      mode: mapping
      occupancy_threshold: 0.1
      odom_frame: odom
      resolution: 0.05
      scan_buffer_maximum_scan_distance: 20.0
      scan_buffer_size: 70
      scan_topic: /fused_scan
      single_scan_topic: /scan
      solver_plugin: solver_plugins::CeresSolver
      stack_size_to_use: 40000000
      tf_buffer_duration: 30.0
      throttle_scans: 1
      transform_publish_period: 0.02
      transform_timeout: 0.2
      use_fusion_scan: false
      use_map_saver: true
      use_response_expansion: true
      use_scan_barycenter: true
      use_scan_matching: true
