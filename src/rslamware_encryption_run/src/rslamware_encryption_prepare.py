#!/usr/bin/env python3
"""
RSlamware Encryption Preparation Script

This script prepares the rslamware launch files and configurations for encryption by:
1. Copying all launch files recursively used in run_rslamware.sh and run_simulator.sh
2. Copying all config files and combining them into one combined_config.yaml
3. Refining launch files to use the combined config
4. Creating a compressed tar.gz archive
5. Creating backup copies
6. Removing original files from install directories
"""

import os
import sys
import shutil
import yaml
import tarfile
import re
from pathlib import Path
from typing import Dict, List, Set, Any
import argparse


class RSlamwareEncryptionPrepare:
    def __init__(self, workspace_root: str):
        self.workspace_root = Path(workspace_root)
        self.install_dir = self.workspace_root / "install"
        self.launch_dir = self.workspace_root / "launch"
        self.config_dir = self.workspace_root / "config"
        self.backup_dir = self.workspace_root / "rslamware_encryption_backup"
        
        # Key packages and launch files from the scripts
        self.key_launch_files = [
            "rslamware_bringup/rslamware.launch.py",
            "cartographer_ros/mapping.launch.py", 
            "nav2_bringup/bringup_launch.py"
        ]
        
        # Track all discovered files
        self.all_launch_files: Set[str] = set()
        self.all_config_files: Set[str] = set()
        self.combined_config: Dict[str, Any] = {}
        
    def create_directories(self):
        """Create necessary directories"""
        self.launch_dir.mkdir(exist_ok=True)
        self.config_dir.mkdir(exist_ok=True)
        self.backup_dir.mkdir(exist_ok=True)
        print(f"Created directories: {self.launch_dir}, {self.config_dir}, {self.backup_dir}")
        
    def find_all_launch_files(self):
        """Recursively find all launch files in install directory"""
        print("Finding all launch files...")
        
        for package_dir in self.install_dir.iterdir():
            if not package_dir.is_dir():
                continue
                
            share_dir = package_dir / "share" / package_dir.name
            if not share_dir.exists():
                continue
                
            launch_share_dir = share_dir / "launch"
            if launch_share_dir.exists():
                for launch_file in launch_share_dir.rglob("*.launch.py"):
                    rel_path = launch_file.relative_to(self.install_dir)
                    self.all_launch_files.add(str(rel_path))
                    
        print(f"Found {len(self.all_launch_files)} launch files")
        
    def find_all_config_files(self):
        """Recursively find all config files in install directory"""
        print("Finding all config files...")
        
        for package_dir in self.install_dir.iterdir():
            if not package_dir.is_dir():
                continue
                
            share_dir = package_dir / "share" / package_dir.name
            if not share_dir.exists():
                continue
                
            config_share_dir = share_dir / "config"
            if config_share_dir.exists():
                for config_file in config_share_dir.rglob("*.yaml"):
                    rel_path = config_file.relative_to(self.install_dir)
                    self.all_config_files.add(str(rel_path))
                for config_file in config_share_dir.rglob("*.yml"):
                    rel_path = config_file.relative_to(self.install_dir)
                    self.all_config_files.add(str(rel_path))
                    
        print(f"Found {len(self.all_config_files)} config files")
        
    def copy_launch_files(self):
        """Copy all launch files to launch directory"""
        print("Copying launch files...")
        
        for launch_file_rel in self.all_launch_files:
            src_path = self.install_dir / launch_file_rel
            dst_path = self.launch_dir / launch_file_rel
            
            # Create parent directories
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file
            shutil.copy2(src_path, dst_path)
            
        print(f"Copied {len(self.all_launch_files)} launch files")
        
    def copy_and_combine_config_files(self):
        """Copy all config files and combine them into one YAML"""
        print("Copying and combining config files...")
        
        # Copy individual files first
        for config_file_rel in self.all_config_files:
            src_path = self.install_dir / config_file_rel
            dst_path = self.config_dir / config_file_rel
            
            # Create parent directories
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file
            shutil.copy2(src_path, dst_path)
            
            # Load and merge into combined config
            try:
                with open(src_path, 'r') as f:
                    config_data = yaml.safe_load(f)
                    if config_data:
                        # Use relative path as key to avoid conflicts
                        config_key = str(config_file_rel).replace('/', '_').replace('.yaml', '').replace('.yml', '')
                        self.combined_config[config_key] = config_data
            except Exception as e:
                print(f"Warning: Could not load {config_file_rel}: {e}")
                
        # Write combined config
        combined_config_path = self.config_dir / "combined_config.yaml"
        with open(combined_config_path, 'w') as f:
            yaml.dump(self.combined_config, f, default_flow_style=False, indent=2)
            
        print(f"Created combined config with {len(self.combined_config)} sections")
        
    def refine_launch_files(self):
        """Refine launch files to use combined_config.yaml"""
        print("Refining launch files to use combined config...")
        
        combined_config_path = "combined_config.yaml"
        
        for launch_file_rel in self.all_launch_files:
            launch_file_path = self.launch_dir / launch_file_rel
            
            try:
                with open(launch_file_path, 'r') as f:
                    content = f.read()
                
                # Replace config file references with combined config
                # This is a simplified approach - in practice, you might need more sophisticated parsing
                patterns = [
                    r"'config/[^']+\.ya?ml'",
                    r'"config/[^"]+\.ya?ml"',
                    r"config/[^\s,)]+\.ya?ml",
                ]
                
                modified = False
                for pattern in patterns:
                    if re.search(pattern, content):
                        content = re.sub(pattern, f"'{combined_config_path}'", content)
                        modified = True
                
                if modified:
                    with open(launch_file_path, 'w') as f:
                        f.write(content)
                        
            except Exception as e:
                print(f"Warning: Could not refine {launch_file_rel}: {e}")
                
        print("Launch file refinement completed")

    def create_tar_archive(self):
        """Create tar.gz archive of launch and config directories"""
        print("Creating tar.gz archive...")

        archive_path = self.workspace_root / "rslamware.tar.gz"

        with tarfile.open(archive_path, 'w:gz') as tar:
            tar.add(self.launch_dir, arcname='launch')
            tar.add(self.config_dir, arcname='config')

        print(f"Created archive: {archive_path}")
        return archive_path

    def create_backup(self, archive_path: Path):
        """Create backup copies in rslamware_encryption_backup"""
        print("Creating backup copies...")

        # Copy archive
        shutil.copy2(archive_path, self.backup_dir / "rslamware.tar.gz")

        # Copy launch and config directories
        if (self.backup_dir / "launch").exists():
            shutil.rmtree(self.backup_dir / "launch")
        if (self.backup_dir / "config").exists():
            shutil.rmtree(self.backup_dir / "config")

        shutil.copytree(self.launch_dir, self.backup_dir / "launch")
        shutil.copytree(self.config_dir, self.backup_dir / "config")

        print(f"Backup created in: {self.backup_dir}")

    def remove_original_files(self):
        """Remove original launch and config files from install directories"""
        print("Removing original files from install directories...")

        removed_launch = 0
        removed_config = 0

        # Remove launch files
        for launch_file_rel in self.all_launch_files:
            original_path = self.install_dir / launch_file_rel
            if original_path.exists():
                original_path.unlink()
                removed_launch += 1

        # Remove config files
        for config_file_rel in self.all_config_files:
            original_path = self.install_dir / config_file_rel
            if original_path.exists():
                original_path.unlink()
                removed_config += 1

        # Remove empty directories
        for package_dir in self.install_dir.iterdir():
            if not package_dir.is_dir():
                continue

            share_dir = package_dir / "share" / package_dir.name
            if not share_dir.exists():
                continue

            for subdir in ["launch", "config"]:
                target_dir = share_dir / subdir
                if target_dir.exists() and not any(target_dir.iterdir()):
                    target_dir.rmdir()

        print(f"Removed {removed_launch} launch files and {removed_config} config files")

    def run(self):
        """Execute the complete preparation process"""
        print("Starting RSlamware encryption preparation...")

        try:
            self.create_directories()
            self.find_all_launch_files()
            self.find_all_config_files()
            self.copy_launch_files()
            self.copy_and_combine_config_files()
            self.refine_launch_files()
            archive_path = self.create_tar_archive()
            self.create_backup(archive_path)
            self.remove_original_files()

            print("RSlamware encryption preparation completed successfully!")
            print(f"Archive created: {archive_path}")
            print(f"Backup available in: {self.backup_dir}")

        except Exception as e:
            print(f"Error during preparation: {e}")
            sys.exit(1)


def main():
    parser = argparse.ArgumentParser(description='Prepare RSlamware files for encryption')
    parser.add_argument('--workspace', default='.',
                       help='Workspace root directory (default: current directory)')

    args = parser.parse_args()

    workspace_root = os.path.abspath(args.workspace)
    if not os.path.exists(os.path.join(workspace_root, 'install')):
        print(f"Error: install directory not found in {workspace_root}")
        print("Please run this script from the workspace root or specify --workspace")
        sys.exit(1)

    preparer = RSlamwareEncryptionPrepare(workspace_root)
    preparer.run()


if __name__ == "__main__":
    main()
