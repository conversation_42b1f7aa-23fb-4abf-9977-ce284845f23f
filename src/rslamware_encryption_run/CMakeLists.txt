cmake_minimum_required(VERSION 3.8)
project(rslamware_encryption_run)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(OpenSSL REQUIRED)

# Find required system packages
find_package(PkgConfig REQUIRED)

# Add executable for encryption/decryption tool
add_executable(rslamware_encryption_run src/rslamware_encryption_run.cpp)

# Link OpenSSL libraries
target_link_libraries(rslamware_encryption_run 
  OpenSSL::SSL 
  OpenSSL::Crypto
)

# Include directories
target_include_directories(rslamware_encryption_run PRIVATE
  ${OpenSSL_INCLUDE_DIRS}
)

# Install the executable
install(TARGETS rslamware_encryption_run
  DESTINATION lib/${PROJECT_NAME}
)

# Install Python scripts
install(PROGRAMS
  src/rslamware_encryption_prepare.py
  DESTINATION lib/${PROJECT_NAME}
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
